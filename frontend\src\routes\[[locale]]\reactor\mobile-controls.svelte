<script lang="ts">
  import type { Common, Reactor } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import LensModal from "./lens-modal.svelte";

  interface Props {
    locale: Common.WebsiteLocale;
    lenses: Reactor.GetLensesOutput;
    selectedLensId: string | null;
    onLensChange: (lensId: string | null) => void;
    onLensesUpdated: () => void;
    onCreatePost: () => void;
  }

  const i18n = {
    en: {
      controls: "Controls",
      lens: "Lens",
      createLens: "Create lens",
      editLens: "Edit lens",
      deleteLens: "Delete lens",
      noLens: "No lens",
      createPost: "Create Post",
      confirmDelete: "Are you sure you want to delete this lens?",
      deleteSuccess: "Lens deleted successfully!",
      deleteError: "Failed to delete lens",
    },
    ru: {
      controls: "Управление",
      lens: "Линза",
      createLens: "Создать линзу",
      editLens: "Редактировать линзу",
      deleteLens: "Удалить линзу",
      noLens: "Без линзы",
      createPost: "Создать пост",
      confirmDelete: "Вы уверены, что хотите удалить эту линзу?",
      deleteSuccess: "Линза успешно удалена!",
      deleteError: "Не удалось удалить линзу",
    },
  };

  const { fetcher: api } = getClient();

  const { locale, lenses, selectedLensId, onLensChange, onLensesUpdated, onCreatePost }: Props =
    $props();

  const t = $derived(i18n[locale]);

  // State
  let isExpanded = $state(false);

  // Modal state
  let showLensModal = $state(false);
  let editingLens = $state<{ id: string; name: string; code: string } | null>(null);

  function toggleExpanded() {
    isExpanded = !isExpanded;
  }

  function handleLensChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    const value = target.value === "" ? null : target.value;
    onLensChange(value);
  }

  function openCreateLensModal() {
    editingLens = null;
    showLensModal = true;
  }

  function openEditLensModal(lens: { id: string; name: string; code: string }) {
    editingLens = lens;
    showLensModal = true;
  }

  function closeLensModal() {
    showLensModal = false;
    editingLens = null;
  }

  async function deleteLens(lensId: string) {
    if (!confirm(t.confirmDelete)) {
      return;
    }

    try {
      await api.reactor.lens.delete({ id: lensId });

      // If the deleted lens was selected, clear selection
      if (selectedLensId === lensId) {
        onLensChange(null);
      }

      onLensesUpdated();
    } catch (error) {
      console.error("Error deleting lens:", error);
      alert(error instanceof Error ? error.message : t.deleteError);
    }
  }

  function handleLensUpdated(updatedLensId?: string) {
    onLensesUpdated();

    // If a lens was updated (not created) and it's currently selected, refetch posts
    if (updatedLensId && selectedLensId === updatedLensId) {
      // Trigger posts refetch by calling onLensChange with the same lens ID
      onLensChange(selectedLensId);
    }
  }
</script>

<div class="mobile-controls">
  <!-- Controls Header -->
  <button
    class="controls-header"
    onclick={toggleExpanded}
    aria-expanded={isExpanded}
    aria-controls="mobile-controls-content"
  >
    <h6 class="mb-0">{t.controls}</h6>
    <i class="bi bi-chevron-{isExpanded ? 'up' : 'down'}"></i>
  </button>

  <!-- Collapsible Content -->
  {#if isExpanded}
    <div class="controls-content" id="mobile-controls-content">
      <!-- First Line: Lens Select -->
      <div class="lens-select-row">
        <select
          class="form-select form-select-sm"
          value={selectedLensId || ""}
          onchange={handleLensChange}
          aria-label={t.lens}
        >
          <option value="">{t.noLens}</option>
          {#each lenses as lens}
            <option value={lens.id}>{lens.name}</option>
          {/each}
        </select>
      </div>

      <!-- Second Line: Lens Controls (left) and Create Post (right) -->
      <div class="controls-row">
        <!-- Left Side: Lens Control Buttons -->
        <div class="lens-controls">
          <button
            class="btn btn-sm btn-outline-primary"
            title={t.createLens}
            aria-label={t.createLens}
            onclick={openCreateLensModal}
          >
            <i class="bi bi-plus"></i>
          </button>
          {#if selectedLensId}
            {@const selectedLens = lenses.find((l) => l.id === selectedLensId)}
            {#if selectedLens}
              <button
                class="btn btn-sm btn-outline-secondary"
                title={t.editLens}
                aria-label={t.editLens}
                onclick={() => openEditLensModal(selectedLens)}
              >
                <i class="bi bi-pencil"></i>
              </button>
              <button
                class="btn btn-sm btn-outline-danger"
                title={t.deleteLens}
                aria-label={t.deleteLens}
                onclick={() => deleteLens(selectedLens.id)}
              >
                <i class="bi bi-trash"></i>
              </button>
            {/if}
          {/if}
        </div>

        <!-- Right Side: Create Post Button -->
        <div class="create-post-control">
          <button
            class="btn btn-sm btn-outline-success"
            onclick={onCreatePost}
            aria-label={t.createPost}
          >
            <i class="bi bi-plus-circle me-1"></i>
            {t.createPost}
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>

<!-- Lens Modal -->
<LensModal
  show={showLensModal}
  onClose={closeLensModal}
  {locale}
  lens={editingLens}
  onLensUpdated={handleLensUpdated}
/>

<style>
  .mobile-controls {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .controls-header {
    padding: 0.75rem 1rem;
    background-color: #e9ecef;
    border: none;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
    width: 100%;
    text-align: left;
  }

  .controls-header:hover {
    background-color: #dee2e6;
  }

  .controls-header:focus {
    outline: 2px solid #0d6efd;
    outline-offset: -2px;
  }

  .controls-content {
    padding: 1rem;
  }

  .lens-select-row {
    margin-bottom: 0.75rem;
  }

  .controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
  }

  .lens-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .lens-controls .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
  }

  .create-post-control .btn {
    white-space: nowrap;
  }

  /* Touch-friendly interactions */
  @media (hover: none) and (pointer: coarse) {
    .controls-header {
      min-height: 48px; /* Minimum touch target size */
      padding: 0.875rem 1rem;
    }

    .lens-controls .btn {
      min-width: 44px;
      min-height: 44px;
    }

    .create-post-control .btn {
      min-height: 44px;
      padding: 0.75rem 1rem;
    }
  }

  /* Responsive adjustments - only stack on very small screens */
  @media (max-width: 480px) {
    .controls-row {
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;
    }

    .lens-controls {
      justify-content: center;
    }

    .create-post-control {
      width: 100%;
    }

    .create-post-control .btn {
      width: 100%;
      justify-content: center;
    }
  }

  /* For screens 481px and above, ensure proper spacing */
  @media (min-width: 481px) {
    .controls-row {
      align-items: center;
    }

    .create-post-control .btn {
      white-space: nowrap;
      min-width: auto;
    }
  }

  /* Tablet adjustments */
  @media (min-width: 577px) and (max-width: 991.98px) {
    .controls-content {
      padding: 1.25rem;
    }

    .lens-select-row {
      margin-bottom: 1rem;
    }
  }
</style>
